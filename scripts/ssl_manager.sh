#!/bin/bash

# Универсальный SSL менеджер для телеграм бота
# Поддерживает Beget хостинг и обычные VPS
# Автоматически настраивает SSL сертификаты

set -e  # Выход при ошибке

echo "🔐 Универсальный SSL менеджер для телеграм бота"
echo "================================================"

# Загружаем переменные окружения
if [ -f "/etc/edu_telebot/env" ]; then
    source /etc/edu_telebot/env
    echo "✅ Переменные окружения загружены"
else
    echo "❌ Файл /etc/edu_telebot/env не найден"
    echo "💡 Запустите сначала: sudo ./scripts/setup_env.sh"
    exit 1
fi

# Проверяем наличие домена
if [ -z "$DOMAIN" ] || [ "$DOMAIN" = "your-domain.com" ]; then
    echo "❌ Домен не настроен в переменных окружения"
    echo "📝 Отредактируйте файл: sudo nano /etc/edu_telebot/env"
    echo "📝 Установите правильное значение для DOMAIN"
    exit 1
fi

echo "🌐 Настраиваем SSL для домена: $DOMAIN"

# Создаем директории
mkdir -p nginx/ssl
mkdir -p logs/ssl

# Функция для проверки DNS записей
check_dns() {
    echo "🔍 Проверяем DNS записи для $DOMAIN..."
    
    # Получаем IP адрес сервера
    SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "unknown")
    echo "📍 IP сервера: $SERVER_IP"
    
    # Проверяем A запись домена
    DOMAIN_IP=$(dig +short $DOMAIN 2>/dev/null | tail -1)
    if [ -n "$DOMAIN_IP" ]; then
        echo "📍 IP домена: $DOMAIN_IP"
        if [ "$SERVER_IP" = "$DOMAIN_IP" ]; then
            echo "✅ DNS записи настроены правильно"
            return 0
        else
            echo "⚠️ DNS записи указывают на другой IP"
            echo "💡 Убедитесь что A запись $DOMAIN указывает на $SERVER_IP"
            return 1
        fi
    else
        echo "❌ Домен $DOMAIN не разрешается"
        echo "💡 Настройте A запись в DNS панели вашего провайдера"
        return 1
    fi
}

# Функция поиска существующих SSL сертификатов
find_existing_ssl() {
    echo "🔍 Ищем существующие SSL сертификаты..."
    
    # Возможные пути к сертификатам
    local cert_paths=(
        "/etc/letsencrypt/live/$DOMAIN"
        "/etc/letsencrypt/live"
        "$HOME/.acme.sh/$DOMAIN"
        "$HOME/.acme.sh"
        "/etc/ssl/certs"
        "/opt/ssl"
        "/var/ssl"
        "/home/<USER>/ssl"
        "/home/<USER>/.acme.sh"
    )

    for path in "${cert_paths[@]}"; do
        # Расширяем wildcards
        for expanded_path in $path; do
            if [ -d "$expanded_path" ]; then
                # Ищем fullchain.pem и privkey.pem
                local fullchain=$(find "$expanded_path" -name "fullchain.pem" -type f 2>/dev/null | head -1)
                local privkey=$(find "$expanded_path" -name "privkey.pem" -type f 2>/dev/null | head -1)

                # Также ищем cert.pem и key.pem
                if [ -z "$fullchain" ]; then
                    fullchain=$(find "$expanded_path" -name "cert.pem" -type f 2>/dev/null | head -1)
                fi
                if [ -z "$privkey" ]; then
                    privkey=$(find "$expanded_path" -name "key.pem" -type f 2>/dev/null | head -1)
                fi

                if [ -n "$fullchain" ] && [ -n "$privkey" ]; then
                    echo "✅ Найдены SSL сертификаты:"
                    echo "   Сертификат: $fullchain"
                    echo "   Ключ: $privkey"

                    # Проверяем срок действия сертификата
                    if command -v openssl &> /dev/null; then
                        local expiry=$(openssl x509 -enddate -noout -in "$fullchain" 2>/dev/null | cut -d= -f2)
                        if [ -n "$expiry" ]; then
                            echo "   Срок действия: $expiry"
                        fi
                    fi

                    read -p "Использовать эти сертификаты? (y/n): " -n 1 -r
                    echo
                    if [[ $REPLY =~ ^[Yy]$ ]]; then
                        # Копируем сертификаты
                        echo "📋 Копируем сертификаты..."
                        sudo cp "$fullchain" nginx/ssl/fullchain.pem
                        sudo cp "$privkey" nginx/ssl/privkey.pem

                        # Устанавливаем права
                        chmod 644 nginx/ssl/fullchain.pem
                        chmod 600 nginx/ssl/privkey.pem
                        chown $USER:$USER nginx/ssl/*.pem 2>/dev/null || true

                        echo "✅ SSL сертификаты настроены"
                        return 0
                    fi
                fi
            fi
        done
    done

    return 1
}

# Функция определения типа хостинга
detect_hosting() {
    echo "🔍 Определяем тип хостинга..."
    
    # Проверяем Beget
    if [ -d "/home/<USER>/domains" ] || [ -d "/var/www/*/data" ] || grep -q "beget" /etc/hostname 2>/dev/null; then
        echo "🏢 Обнаружен Beget хостинг"
        return 1
    fi
    
    # Проверяем другие популярные хостинги
    if [ -d "/usr/local/mgr5" ]; then
        echo "🏢 Обнаружен ISPmanager"
        return 2
    fi
    
    if [ -d "/usr/local/cpanel" ]; then
        echo "🏢 Обнаружен cPanel"
        return 3
    fi
    
    echo "🖥️ Обычный VPS/сервер"
    return 0
}

# Функция установки зависимостей
install_dependencies() {
    echo "📦 Проверяем и устанавливаем зависимости..."
    
    # Обновляем пакеты
    sudo apt update -qq
    
    # Устанавливаем необходимые пакеты
    local packages=("curl" "socat" "cron" "openssl")
    for package in "${packages[@]}"; do
        if ! command -v $package &> /dev/null; then
            echo "📦 Устанавливаем $package..."
            sudo apt install -y $package
        else
            echo "✅ $package уже установлен"
        fi
    done
}

# Функция установки acme.sh
install_acme() {
    echo "📦 Устанавливаем acme.sh..."
    
    if [ -d "$HOME/.acme.sh" ]; then
        echo "✅ acme.sh уже установлен"
        return 0
    fi
    
    # Устанавливаем acme.sh
    curl https://get.acme.sh | sh -s email=admin@$DOMAIN
    
    if [ -d "$HOME/.acme.sh" ]; then
        echo "✅ acme.sh установлен успешно"
        # Добавляем в PATH
        export PATH="$HOME/.acme.sh:$PATH"
        return 0
    else
        echo "❌ Ошибка установки acme.sh"
        return 1
    fi
}

# Функция получения SSL через HTTP валидацию
get_ssl_http() {
    echo "🔐 Получаем SSL сертификат через HTTP валидацию..."
    
    # Создаем временную директорию для валидации
    local webroot="/tmp/acme_webroot"
    mkdir -p "$webroot"
    
    # Запускаем временный веб-сервер для валидации
    echo "🌐 Запускаем временный веб-сервер на порту 80..."
    
    # Останавливаем nginx если запущен
    if command -v docker-compose &> /dev/null && [ -f "docker-compose.yml" ]; then
        echo "🛑 Временно останавливаем nginx..."
        docker-compose stop nginx 2>/dev/null || true
    fi
    
    # Получаем сертификат
    if $HOME/.acme.sh/acme.sh --issue -d $DOMAIN --standalone --httpport 80; then
        echo "✅ SSL сертификат получен успешно"
        
        # Копируем сертификаты
        $HOME/.acme.sh/acme.sh --install-cert -d $DOMAIN \
            --cert-file nginx/ssl/cert.pem \
            --key-file nginx/ssl/privkey.pem \
            --fullchain-file nginx/ssl/fullchain.pem
        
        # Устанавливаем права
        chmod 644 nginx/ssl/*.pem
        chmod 600 nginx/ssl/privkey.pem
        chown $USER:$USER nginx/ssl/*.pem 2>/dev/null || true
        
        echo "✅ SSL сертификаты установлены"
        return 0
    else
        echo "❌ Ошибка получения SSL сертификата"
        return 1
    fi
}

# Функция настройки автообновления
setup_auto_renewal() {
    echo "🔄 Настраиваем автообновление SSL сертификатов..."
    
    # Создаем скрипт обновления
    cat > /tmp/ssl_renewal.sh << 'EOF'
#!/bin/bash
# Скрипт автообновления SSL сертификатов

cd /path/to/project
source /etc/edu_telebot/env

# Обновляем сертификаты
$HOME/.acme.sh/acme.sh --renew -d $DOMAIN --force

# Перезапускаем nginx
if command -v docker-compose &> /dev/null; then
    docker-compose restart nginx
fi

echo "$(date): SSL сертификаты обновлены" >> logs/ssl/renewal.log
EOF
    
    # Заменяем путь к проекту
    sed -i "s|/path/to/project|$(pwd)|g" /tmp/ssl_renewal.sh
    
    # Копируем скрипт
    sudo cp /tmp/ssl_renewal.sh /etc/cron.daily/ssl-renewal
    sudo chmod +x /etc/cron.daily/ssl-renewal
    sudo chown root:root /etc/cron.daily/ssl-renewal
    
    echo "✅ Автообновление настроено (ежедневная проверка)"
    rm /tmp/ssl_renewal.sh
}

# Основная функция
main() {
    echo "🚀 Начинаем настройку SSL..."
    
    # Проверяем DNS
    if ! check_dns; then
        echo "⚠️ DNS записи не настроены правильно"
        read -p "Продолжить несмотря на это? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ Настройте DNS записи и запустите скрипт снова"
            exit 1
        fi
    fi
    
    # Ищем существующие сертификаты
    if find_existing_ssl; then
        echo "🎉 Используем найденные SSL сертификаты"
        setup_auto_renewal
        return 0
    fi
    
    # Определяем тип хостинга
    detect_hosting
    hosting_type=$?
    
    # Устанавливаем зависимости
    install_dependencies
    
    # Устанавливаем acme.sh
    if ! install_acme; then
        echo "❌ Не удалось установить acme.sh"
        exit 1
    fi
    
    # Получаем SSL сертификат
    if get_ssl_http; then
        echo "🎉 SSL сертификаты успешно настроены!"
        setup_auto_renewal
        
        echo ""
        echo "✅ SSL настройка завершена!"
        echo "📁 Сертификаты сохранены в: nginx/ssl/"
        echo "🔄 Автообновление настроено"
        echo "📝 Логи обновлений: logs/ssl/renewal.log"
        
        return 0
    else
        echo "❌ Не удалось получить SSL сертификаты"
        echo "💡 Возможные причины:"
        echo "   - Домен не указывает на этот сервер"
        echo "   - Порт 80 заблокирован"
        echo "   - Проблемы с DNS"
        echo "💡 Проверьте настройки и попробуйте снова"
        return 1
    fi
}

# Запускаем основную функцию
main "$@"
